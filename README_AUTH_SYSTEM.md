# Système d'Authentification et Redirection par Rôles

## Vue d'ensemble

Le système d'authentification de l'application de gestion de stock pour bar-restaurant implémente une redirection automatique vers des dashboards spécifiques selon le rôle de l'utilisateur connecté.

## Architecture

### Composants principaux

1. **MainWindow** (`src/views/main_window.py`)
   - Fenêtre principale qui gère la redirection
   - Affiche la fenêtre de connexion au démarrage
   - Redirige vers le dashboard approprié après authentification

2. **LoginWindow** (`src/views/login_window.py`)
   - Interface de connexion
   - Émet un signal `login_successful` avec les données utilisateur

3. **Dashboards spécialisés**
   - `AdminDashboard` : Accès complet (gestion utilisateurs, produits, stock, ventes, dépenses, rapports)
   - `GerantDashboard` : Gestion sans administration utilisateurs (produits, stock, ventes, approvisionnements, dépenses, rapports)
   - `ServeurDashboard` : Accès limité aux ventes (nouvelle vente, historique)

## Flux d'authentification

```
1. Démarrage → MainWindow
2. MainWindow → LoginWindow (automatique)
3. Utilisateur saisit identifiants
4. LoginWindow → AuthController.login()
5. Si succès → Signal login_successful
6. MainWindow reçoit signal → Redirection selon rôle
7. Affichage du dashboard approprié
```

## Rôles et permissions

### Administrateur (`admin`)
- **Dashboard** : Panneau d'Administration
- **Fonctionnalités** :
  - Gestion des utilisateurs (création, modification, suppression)
  - Gestion des produits
  - Gestion du stock
  - Gestion des ventes
  - Gestion des dépenses
  - Génération de rapports
- **Interface** : 6 boutons principaux en grille 3x2

### Gérant (`gerant`)
- **Dashboard** : Panneau de Gestion
- **Fonctionnalités** :
  - Gestion des produits
  - Gestion du stock
  - Gestion des ventes
  - Gestion des approvisionnements
  - Gestion des dépenses
  - Génération de rapports
- **Restrictions** : Pas d'accès à la gestion des utilisateurs
- **Interface** : 6 boutons principaux en grille 3x2

### Serveur (`serveur`)
- **Dashboard** : Point de Vente
- **Fonctionnalités** :
  - Nouvelle vente
  - Consultation de l'historique des ventes (lecture seule)
- **Restrictions** : Accès limité aux ventes uniquement
- **Interface** : 2 boutons principaux

## Comptes de test

Utilisez le script `test_auth_system.py` pour créer des comptes de test :

```bash
python test_auth_system.py
```

### Comptes créés :

1. **Administrateur**
   - Nom d'utilisateur : `admin_test`
   - Mot de passe : `admin123`

2. **Gérant**
   - Nom d'utilisateur : `gerant_test`
   - Mot de passe : `gerant123`

3. **Serveur**
   - Nom d'utilisateur : `serveur_test`
   - Mot de passe : `serveur123`

## Utilisation

### Lancement de l'application
```bash
python main.py
```

### Test du système
1. Lancez l'application
2. Utilisez un des comptes de test
3. Vérifiez la redirection vers le bon dashboard
4. Testez la déconnexion (bouton rouge en haut à droite)
5. Reconnectez-vous avec un autre rôle

## Fonctionnalités communes

### Tous les dashboards incluent :
- **En-tête** : Nom de l'utilisateur, rôle, date/heure en temps réel
- **Bouton de déconnexion** : Retour à l'écran de connexion
- **Style cohérent** : Interface moderne avec couleurs Bootstrap
- **Responsive** : Adaptation à différentes tailles d'écran

### Gestion des sessions
- Session automatique lors de la connexion
- Déconnexion propre avec nettoyage de session
- Fermeture d'application avec déconnexion automatique

## Sécurité

- **Hachage des mots de passe** : bcrypt avec salt
- **Validation des rôles** : Vérification côté serveur
- **Sessions sécurisées** : Gestion centralisée via AuthController
- **Permissions granulaires** : Contrôle d'accès basé sur les rôles

## Développement futur

Les boutons des dashboards affichent actuellement des messages d'information. Les prochaines étapes incluront :

1. Implémentation des fenêtres de gestion spécifiques
2. Intégration avec les modèles de données
3. Fonctionnalités CRUD complètes
4. Rapports et statistiques
5. Système d'alertes et notifications

## Structure des fichiers

```
src/
├── views/
│   ├── main_window.py          # Fenêtre principale et redirection
│   ├── login_window.py         # Interface de connexion
│   ├── admin_dashboard.py      # Dashboard administrateur
│   ├── gerant_dashboard.py     # Dashboard gérant
│   └── serveur_dashboard.py    # Dashboard serveur
├── controllers/
│   └── auth_controller.py      # Contrôleur d'authentification
└── models/
    └── user.py                 # Modèle utilisateur
```

## Notes techniques

- **Framework** : PyQt5 pour l'interface graphique
- **Base de données** : SQLite avec SQLAlchemy ORM
- **Architecture** : MVC (Model-View-Controller)
- **Signaux PyQt** : Communication entre fenêtres
- **Styles** : CSS intégré pour l'apparence moderne
