# -*- coding: utf-8 -*-
"""
Contrôleur d'authentification
"""

from datetime import datetime
from sqlalchemy.orm import Session
from models.database import SessionLocal
from models.user import User
from utils.config import PERMISSIONS
import bcrypt

class AuthController:
    def __init__(self):
        self.current_user = None
        self.db = SessionLocal()

    def login(self, username, password):
        """
        Authentifier un utilisateur

        Args:
            username (str): Nom d'utilisateur
            password (str): Mot de passe

        Returns:
            dict: Résultat de l'authentification
        """
        try:
            # Rechercher l'utilisateur
            user = self.db.query(User).filter(
                User.username == username,
                User.is_active == True
            ).first()

            if not user:
                return {
                    'success': False,
                    'message': 'Nom d\'utilisateur ou mot de passe incorrect',
                    'user': None
                }

            # Vérifier le mot de passe
            if not user.check_password(password):
                return {
                    'success': False,
                    'message': 'Nom d\'utilisateur ou mot de passe incorrect',
                    'user': None
                }

            # Mettre à jour la dernière connexion
            user.last_login = datetime.now()
            self.db.commit()

            # Définir l'utilisateur actuel
            self.current_user = user

            return {
                'success': True,
                'message': f'Connexion réussie. Bienvenue {user.full_name}!',
                'user': user.to_dict()
            }

        except Exception as e:
            self.db.rollback()
            return {
                'success': False,
                'message': f'Erreur lors de la connexion: {str(e)}',
                'user': None
            }

    def logout(self):
        """Déconnecter l'utilisateur actuel"""
        self.current_user = None
        return {
            'success': True,
            'message': 'Déconnexion réussie'
        }

    def is_authenticated(self):
        """Vérifier si un utilisateur est connecté"""
        return self.current_user is not None

    def has_permission(self, permission):
        """
        Vérifier si l'utilisateur actuel a une permission spécifique

        Args:
            permission (str): Permission à vérifier

        Returns:
            bool: True si l'utilisateur a la permission
        """
        if not self.is_authenticated():
            return False

        return self.current_user.has_permission(permission)

    def get_current_user(self):
        """Obtenir l'utilisateur actuellement connecté"""
        return self.current_user

    def get_user_role(self):
        """Obtenir le rôle de l'utilisateur actuel"""
        if not self.is_authenticated():
            return None
        return self.current_user.role

    def get_user_permissions(self):
        """Obtenir toutes les permissions de l'utilisateur actuel"""
        if not self.is_authenticated():
            return []

        return PERMISSIONS.get(self.current_user.role, [])

    def change_password(self, old_password, new_password):
        """
        Changer le mot de passe de l'utilisateur actuel

        Args:
            old_password (str): Ancien mot de passe
            new_password (str): Nouveau mot de passe

        Returns:
            dict: Résultat de l'opération
        """
        if not self.is_authenticated():
            return {
                'success': False,
                'message': 'Aucun utilisateur connecté'
            }

        try:
            # Vérifier l'ancien mot de passe
            if not self.current_user.check_password(old_password):
                return {
                    'success': False,
                    'message': 'Ancien mot de passe incorrect'
                }

            # Définir le nouveau mot de passe
            self.current_user.set_password(new_password)
            self.db.commit()

            return {
                'success': True,
                'message': 'Mot de passe changé avec succès'
            }

        except Exception as e:
            self.db.rollback()
            return {
                'success': False,
                'message': f'Erreur lors du changement de mot de passe: {str(e)}'
            }

    def __del__(self):
        """Fermer la session de base de données"""
        if hasattr(self, 'db'):
            self.db.close()

# Instance globale du contrôleur d'authentification
auth_controller = AuthController()
