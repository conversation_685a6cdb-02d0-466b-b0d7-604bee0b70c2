# -*- coding: utf-8 -*-
"""
Configuration et initialisation de la base de données
"""

import os
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from utils.config import DATABASE_URL, DATA_DIR
import bcrypt

# C<PERSON>er le dossier data s'il n'existe pas
os.makedirs(DATA_DIR, exist_ok=True)

# Configuration SQLAlchemy
engine = create_engine(DATABASE_URL, echo=False)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

def get_db():
    """Obtenir une session de base de données"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def init_database():
    """Initialiser la base de données et créer les tables"""
    from models.user import User
    from models.product import Product
    from models.sale import Sale, SaleItem
    from models.supply import Supply
    from models.expense import Expense

    # C<PERSON>er toutes les tables
    Base.metadata.create_all(bind=engine)

    # C<PERSON>er l'utilisateur admin par défaut s'il n'existe pas
    db = SessionLocal()
    try:
        admin_user = db.query(User).filter(User.username == 'admin').first()
        if not admin_user:
            # Hacher le mot de passe par défaut
            password_hash = bcrypt.hashpw('admin123'.encode('utf-8'), bcrypt.gensalt())

            admin_user = User(
                username='admin',
                password_hash=password_hash.decode('utf-8'),
                role='admin',
                full_name='Administrateur',
                is_active=True
            )
            db.add(admin_user)
            db.commit()
            print("✅ Utilisateur admin créé avec succès")
            print("   Nom d'utilisateur: admin")
            print("   Mot de passe: admin123")
            print("   ⚠️  Changez ce mot de passe lors de la première connexion!")
    except Exception as e:
        print(f"❌ Erreur lors de la création de l'admin: {e}")
        db.rollback()
    finally:
        db.close()
