# -*- coding: utf-8 -*-
"""
Fenêtre de gestion des produits
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QTableWidget, QTableWidgetItem, 
                             QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                             QMessageBox, QHeaderView, QFrame, QFormLayout,
                             QGroupBox, QGridLayout)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from models.database import SessionLocal
from models.product import Product
from datetime import datetime

class ProductManagement(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db = SessionLocal()
        self.init_ui()
        self.load_products()
        
    def init_ui(self):
        """Initialiser l'interface utilisateur"""
        self.setWindowTitle("Gestion des Produits")
        self.setMinimumSize(1000, 700)
        self.setModal(True)
        
        # Layout principal
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # En-tête
        header = QLabel("GESTION DES PRODUITS")
        header.setFont(QFont("Arial", 18, QFont.Bold))
        header.setAlignment(Qt.AlignCenter)
        header.setStyleSheet("color: #2c3e50; margin-bottom: 20px;")
        main_layout.addWidget(header)
        
        # Section de recherche et filtres
        search_layout = self.create_search_section()
        main_layout.addLayout(search_layout)
        
        # Tableau des produits
        self.create_products_table()
        main_layout.addWidget(self.products_table)
        
        # Boutons d'action
        buttons_layout = self.create_buttons_section()
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
        # Style général
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                font-family: Arial, sans-serif;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
            QTableWidget {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                gridline-color: #dee2e6;
            }
            QTableWidget::item {
                padding: 8px;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox {
                padding: 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: white;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
    
    def create_search_section(self):
        """Créer la section de recherche et filtres"""
        search_layout = QHBoxLayout()
        
        # Recherche par nom
        search_label = QLabel("Rechercher:")
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Nom du produit...")
        self.search_input.textChanged.connect(self.filter_products)
        
        # Filtre par catégorie
        category_label = QLabel("Catégorie:")
        self.category_filter = QComboBox()
        self.category_filter.addItem("Toutes les catégories")
        self.load_categories()
        self.category_filter.currentTextChanged.connect(self.filter_products)
        
        # Filtre par statut
        status_label = QLabel("Statut:")
        self.status_filter = QComboBox()
        self.status_filter.addItems(["Tous", "Actif", "Inactif", "Stock bas"])
        self.status_filter.currentTextChanged.connect(self.filter_products)
        
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        search_layout.addWidget(category_label)
        search_layout.addWidget(self.category_filter)
        search_layout.addWidget(status_label)
        search_layout.addWidget(self.status_filter)
        search_layout.addStretch()
        
        return search_layout
    
    def create_products_table(self):
        """Créer le tableau des produits"""
        self.products_table = QTableWidget()
        self.products_table.setColumnCount(8)
        self.products_table.setHorizontalHeaderLabels([
            "Nom", "Catégorie", "Prix Achat", "Prix Vente", 
            "Stock Actuel", "Stock Min", "Unité", "Statut"
        ])
        
        # Configuration du tableau
        header = self.products_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # Nom
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Catégorie
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Prix Achat
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Prix Vente
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Stock Actuel
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Stock Min
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Unité
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # Statut
        
        self.products_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.products_table.setAlternatingRowColors(True)
        self.products_table.doubleClicked.connect(self.edit_product)
    
    def create_buttons_section(self):
        """Créer la section des boutons"""
        buttons_layout = QHBoxLayout()
        
        # Bouton Nouveau produit
        new_button = QPushButton("Nouveau Produit")
        new_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                font-size: 14px;
                padding: 12px 24px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        new_button.clicked.connect(self.add_product)
        
        # Bouton Modifier
        edit_button = QPushButton("Modifier")
        edit_button.setStyleSheet("""
            QPushButton {
                background-color: #ffc107;
                color: #212529;
                font-size: 14px;
                padding: 12px 24px;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
        """)
        edit_button.clicked.connect(self.edit_product)
        
        # Bouton Supprimer
        delete_button = QPushButton("Supprimer")
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                font-size: 14px;
                padding: 12px 24px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        delete_button.clicked.connect(self.delete_product)
        
        # Bouton Actualiser
        refresh_button = QPushButton("Actualiser")
        refresh_button.clicked.connect(self.load_products)
        
        # Bouton Fermer
        close_button = QPushButton("Fermer")
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                font-size: 14px;
                padding: 12px 24px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        close_button.clicked.connect(self.close)
        
        buttons_layout.addWidget(new_button)
        buttons_layout.addWidget(edit_button)
        buttons_layout.addWidget(delete_button)
        buttons_layout.addStretch()
        buttons_layout.addWidget(refresh_button)
        buttons_layout.addWidget(close_button)
        
        return buttons_layout
    
    def load_categories(self):
        """Charger les catégories disponibles"""
        try:
            categories = self.db.query(Product.category).distinct().all()
            for category in categories:
                if category[0]:  # Vérifier que la catégorie n'est pas None
                    self.category_filter.addItem(category[0])
        except Exception as e:
            print(f"Erreur lors du chargement des catégories: {e}")
    
    def load_products(self):
        """Charger les produits dans le tableau"""
        try:
            products = self.db.query(Product).all()
            self.products_table.setRowCount(len(products))
            
            for row, product in enumerate(products):
                # Nom
                self.products_table.setItem(row, 0, QTableWidgetItem(product.name))
                
                # Catégorie
                self.products_table.setItem(row, 1, QTableWidgetItem(product.category or ""))
                
                # Prix d'achat
                self.products_table.setItem(row, 2, QTableWidgetItem(f"{product.purchase_price:.0f} FBU"))
                
                # Prix de vente
                self.products_table.setItem(row, 3, QTableWidgetItem(f"{product.selling_price:.0f} FBU"))
                
                # Stock actuel
                stock_item = QTableWidgetItem(str(product.current_stock))
                if product.is_low_stock:
                    stock_item.setBackground(Qt.red)
                    stock_item.setForeground(Qt.white)
                self.products_table.setItem(row, 4, stock_item)
                
                # Stock minimum
                self.products_table.setItem(row, 5, QTableWidgetItem(str(product.min_stock)))
                
                # Unité
                self.products_table.setItem(row, 6, QTableWidgetItem(product.unit or ""))
                
                # Statut
                status = "Actif" if product.is_active == "Oui" else "Inactif"
                if product.is_low_stock and product.is_active == "Oui":
                    status = "Stock bas"
                self.products_table.setItem(row, 7, QTableWidgetItem(status))
                
                # Stocker l'ID du produit dans la première colonne
                self.products_table.item(row, 0).setData(Qt.UserRole, product.id)
                
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des produits: {e}")
    
    def filter_products(self):
        """Filtrer les produits selon les critères"""
        search_text = self.search_input.text().lower()
        category_filter = self.category_filter.currentText()
        status_filter = self.status_filter.currentText()
        
        for row in range(self.products_table.rowCount()):
            show_row = True
            
            # Filtre par nom
            if search_text:
                name = self.products_table.item(row, 0).text().lower()
                if search_text not in name:
                    show_row = False
            
            # Filtre par catégorie
            if category_filter != "Toutes les catégories":
                category = self.products_table.item(row, 1).text()
                if category != category_filter:
                    show_row = False
            
            # Filtre par statut
            if status_filter != "Tous":
                status = self.products_table.item(row, 7).text()
                if status_filter == "Actif" and status not in ["Actif", "Stock bas"]:
                    show_row = False
                elif status_filter == "Inactif" and status != "Inactif":
                    show_row = False
                elif status_filter == "Stock bas" and status != "Stock bas":
                    show_row = False
            
            self.products_table.setRowHidden(row, not show_row)
    
    def add_product(self):
        """Ajouter un nouveau produit"""
        dialog = ProductDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_products()
    
    def edit_product(self):
        """Modifier le produit sélectionné"""
        current_row = self.products_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "Attention", "Veuillez sélectionner un produit à modifier.")
            return
        
        product_id = self.products_table.item(current_row, 0).data(Qt.UserRole)
        product = self.db.query(Product).filter(Product.id == product_id).first()
        
        if product:
            dialog = ProductDialog(self, product)
            if dialog.exec_() == QDialog.Accepted:
                self.load_products()
    
    def delete_product(self):
        """Supprimer le produit sélectionné"""
        current_row = self.products_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "Attention", "Veuillez sélectionner un produit à supprimer.")
            return
        
        product_name = self.products_table.item(current_row, 0).text()
        reply = QMessageBox.question(
            self, 
            'Confirmation',
            f'Êtes-vous sûr de vouloir supprimer le produit "{product_name}"?',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                product_id = self.products_table.item(current_row, 0).data(Qt.UserRole)
                product = self.db.query(Product).filter(Product.id == product_id).first()
                
                if product:
                    self.db.delete(product)
                    self.db.commit()
                    QMessageBox.information(self, "Succès", "Produit supprimé avec succès!")
                    self.load_products()
                    
            except Exception as e:
                self.db.rollback()
                QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression: {e}")
    
    def closeEvent(self, event):
        """Fermer la session de base de données"""
        self.db.close()
        event.accept()


class ProductDialog(QDialog):
    def __init__(self, parent=None, product=None):
        super().__init__(parent)
        self.db = SessionLocal()
        self.product = product
        self.is_edit_mode = product is not None
        self.init_ui()
        
        if self.is_edit_mode:
            self.load_product_data()
    
    def init_ui(self):
        """Initialiser l'interface du dialogue"""
        title = "Modifier le Produit" if self.is_edit_mode else "Nouveau Produit"
        self.setWindowTitle(title)
        self.setMinimumSize(500, 600)
        self.setModal(True)
        
        # Layout principal
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # En-tête
        header = QLabel(title)
        header.setFont(QFont("Arial", 16, QFont.Bold))
        header.setAlignment(Qt.AlignCenter)
        header.setStyleSheet("color: #2c3e50; margin-bottom: 20px;")
        main_layout.addWidget(header)
        
        # Formulaire
        form_layout = self.create_form()
        main_layout.addLayout(form_layout)
        
        # Boutons
        buttons_layout = self.create_dialog_buttons()
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
        # Style
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                font-family: Arial, sans-serif;
            }
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox {
                padding: 10px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: white;
                font-size: 14px;
            }
            QLabel {
                font-weight: bold;
                color: #495057;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
    
    def create_form(self):
        """Créer le formulaire de saisie"""
        form_layout = QFormLayout()
        form_layout.setSpacing(15)
        
        # Nom du produit
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("Nom du produit")
        form_layout.addRow("Nom du produit *:", self.name_input)
        
        # Catégorie
        self.category_input = QComboBox()
        self.category_input.setEditable(True)
        self.load_categories()
        form_layout.addRow("Catégorie:", self.category_input)
        
        # Prix d'achat
        self.purchase_price_input = QDoubleSpinBox()
        self.purchase_price_input.setRange(0, 999999)
        self.purchase_price_input.setSuffix(" FBU")
        form_layout.addRow("Prix d'achat *:", self.purchase_price_input)
        
        # Prix de vente
        self.selling_price_input = QDoubleSpinBox()
        self.selling_price_input.setRange(0, 999999)
        self.selling_price_input.setSuffix(" FBU")
        form_layout.addRow("Prix de vente *:", self.selling_price_input)
        
        # Stock actuel
        self.current_stock_input = QSpinBox()
        self.current_stock_input.setRange(0, 999999)
        form_layout.addRow("Stock actuel:", self.current_stock_input)
        
        # Stock minimum
        self.min_stock_input = QSpinBox()
        self.min_stock_input.setRange(0, 999999)
        self.min_stock_input.setValue(5)  # Valeur par défaut
        form_layout.addRow("Stock minimum *:", self.min_stock_input)
        
        # Unité
        self.unit_input = QComboBox()
        self.unit_input.setEditable(True)
        self.unit_input.addItems(["pièce", "litre", "kg", "bouteille", "carton", "paquet"])
        form_layout.addRow("Unité:", self.unit_input)
        
        # Statut
        self.status_input = QComboBox()
        self.status_input.addItems(["Oui", "Non"])
        form_layout.addRow("Produit actif:", self.status_input)
        
        return form_layout
    
    def load_categories(self):
        """Charger les catégories existantes"""
        try:
            categories = self.db.query(Product.category).distinct().all()
            for category in categories:
                if category[0]:
                    self.category_input.addItem(category[0])
        except Exception as e:
            print(f"Erreur lors du chargement des catégories: {e}")
    
    def load_product_data(self):
        """Charger les données du produit à modifier"""
        if self.product:
            self.name_input.setText(self.product.name)
            self.category_input.setCurrentText(self.product.category or "")
            self.purchase_price_input.setValue(self.product.purchase_price)
            self.selling_price_input.setValue(self.product.selling_price)
            self.current_stock_input.setValue(self.product.current_stock)
            self.min_stock_input.setValue(self.product.min_stock)
            self.unit_input.setCurrentText(self.product.unit or "")
            self.status_input.setCurrentText(self.product.is_active)
    
    def create_dialog_buttons(self):
        """Créer les boutons du dialogue"""
        buttons_layout = QHBoxLayout()
        
        # Bouton Enregistrer
        save_button = QPushButton("Enregistrer")
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        save_button.clicked.connect(self.save_product)
        
        # Bouton Annuler
        cancel_button = QPushButton("Annuler")
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        cancel_button.clicked.connect(self.reject)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(cancel_button)
        
        return buttons_layout
    
    def save_product(self):
        """Enregistrer le produit"""
        # Validation
        if not self.name_input.text().strip():
            QMessageBox.warning(self, "Attention", "Le nom du produit est obligatoire.")
            return
        
        if self.purchase_price_input.value() <= 0:
            QMessageBox.warning(self, "Attention", "Le prix d'achat doit être supérieur à 0.")
            return
        
        if self.selling_price_input.value() <= 0:
            QMessageBox.warning(self, "Attention", "Le prix de vente doit être supérieur à 0.")
            return
        
        try:
            if self.is_edit_mode:
                # Modifier le produit existant
                self.product.name = self.name_input.text().strip()
                self.product.category = self.category_input.currentText().strip()
                self.product.purchase_price = self.purchase_price_input.value()
                self.product.selling_price = self.selling_price_input.value()
                self.product.current_stock = self.current_stock_input.value()
                self.product.min_stock = self.min_stock_input.value()
                self.product.unit = self.unit_input.currentText().strip()
                self.product.is_active = self.status_input.currentText()
                self.product.updated_at = datetime.now()
            else:
                # Créer un nouveau produit
                new_product = Product(
                    name=self.name_input.text().strip(),
                    category=self.category_input.currentText().strip(),
                    purchase_price=self.purchase_price_input.value(),
                    selling_price=self.selling_price_input.value(),
                    current_stock=self.current_stock_input.value(),
                    min_stock=self.min_stock_input.value(),
                    unit=self.unit_input.currentText().strip(),
                    is_active=self.status_input.currentText()
                )
                self.db.add(new_product)
            
            self.db.commit()
            
            action = "modifié" if self.is_edit_mode else "créé"
            QMessageBox.information(self, "Succès", f"Produit {action} avec succès!")
            self.accept()
            
        except Exception as e:
            self.db.rollback()
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement: {e}")
    
    def closeEvent(self, event):
        """Fermer la session de base de données"""
        self.db.close()
        event.accept()
