# 🍽️ Application de Gestion de Stock - Bar-Restaurant

Application desktop développée en Python pour la gestion complète d'un bar-restaurant au Burundi.

## 🎯 Fonctionnalités

### 🔐 Authentification et Rôles
- **Admin** : Accès complet à toutes les fonctionnalités
- **Gérant** : Gestion quotidienne (pas de gestion utilisateurs ni suppression)
- **Serveur** : Interface simplifiée pour les ventes uniquement

### 📦 Gestion des Stocks
- Ajout/modification/suppression de produits
- Catégories : Boissons, Plats, Snacks
- Alertes automatiques pour stocks bas
- Suivi des approvisionnements

### 💰 Ventes et Commandes
- Interface de prise de commande par table
- Calcul automatique des totaux
- Génération de factures
- Historique des ventes

### 📊 Rapports et Analyses
- Rapports quotidiens et mensuels
- Export PDF et Excel
- Statistiques de vente
- Suivi des dépenses

## 🛠️ Technologies Utilisées

- **Interface** : PyQt5
- **Base de données** : SQLite avec SQLAlchemy
- **Sécurité** : bcrypt pour le hachage des mots de passe
- **Rapports** : reportlab (PDF), openpyxl (Excel)
- **Déploiement** : PyInstaller

## 📁 Structure du Projet

```
Br_Stock/
├── main.py                 # Point d'entrée principal
├── requirements.txt        # Dépendances Python
├── src/
│   ├── models/            # Modèles de données (SQLAlchemy)
│   ├── views/             # Interfaces utilisateur (PyQt5)
│   ├── controllers/       # Logique métier
│   └── utils/             # Utilitaires et configuration
├── data/                  # Base de données SQLite
├── reports/               # Rapports générés
├── assets/                # Images et icônes
└── tests/                 # Tests unitaires
```

## 🚀 Installation et Utilisation

### Prérequis
- Python 3.8+
- pip

### Installation
```bash
# Cloner le projet
git clone <repository-url>
cd Br_Stock

# Installer les dépendances
pip install -r requirements.txt

# Lancer l'application
python main.py
```

### Première utilisation
Au premier lancement, un utilisateur admin par défaut sera créé :
- **Nom d'utilisateur** : admin
- **Mot de passe** : admin123

⚠️ **Important** : Changez ce mot de passe lors de la première connexion !

## 📋 Utilisation par Rôle

### 👨‍💼 Administrateur
- Gestion complète des utilisateurs
- Configuration des produits et prix
- Accès à tous les rapports
- Sauvegarde et restauration de données

### 👨‍💻 Gérant
- Gestion des produits et stocks
- Enregistrement des ventes
- Génération de rapports
- Suivi des dépenses

### 👨‍🍳 Serveur
- Interface simplifiée de vente
- Prise de commandes par table
- Génération de factures clients

## 🔧 Développement

### Tests
```bash
python -m pytest tests/
```

### Génération de l'exécutable
```bash
pyinstaller --onefile --noconsole main.py
```

## 📞 Support

Pour toute question ou problème, contactez le développeur.

---
**Développé avec ❤️ pour les bars-restaurants du Burundi**
