# -*- coding: utf-8 -*-
"""
Fenêtre principale qui gère la redirection vers les dashboards selon le rôle
"""

import sys
from PyQt5.QtWidgets import QMainWindow, QApplication
from PyQt5.QtCore import Qt
from controllers.auth_controller import auth_controller
from views.login_window import Lo<PERSON><PERSON>indow
from views.admin_dashboard import AdminDashboard
from views.gerant_dashboard import GerantDashboard
from views.serveur_dashboard import ServeurDashboard

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.current_dashboard = None
        self.init_ui()

    def init_ui(self):
        """Initialiser l'interface utilisateur"""
        self.setWindowTitle("Gestion Stock Bar-Restaurant")
        self.setMinimumSize(1200, 800)

        # Centrer la fenêtre
        self.center_window()

        # Afficher la fenêtre de connexion
        self.show_login()

    def center_window(self):
        """Centrer la fenêtre sur l'écran"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )

    def show_login(self):
        """Afficher la fenêtre de connexion"""
        self.login_window = LoginWindow()
        self.login_window.login_successful.connect(self.on_login_successful)
        self.login_window.show()
        self.hide()  # Cacher la fenêtre principale

    def on_login_successful(self, user_data):
        """Gérer la connexion réussie et rediriger vers le dashboard approprié"""
        user_role = user_data.get('role')

        # Fermer la fenêtre de connexion
        if hasattr(self, 'login_window'):
            self.login_window.close()

        # Rediriger vers le dashboard approprié selon le rôle
        if user_role == 'admin':
            self.show_admin_dashboard()
        elif user_role == 'gerant':
            self.show_gerant_dashboard()
        elif user_role == 'serveur':
            self.show_serveur_dashboard()
        else:
            # Rôle non reconnu, retourner à la connexion
            self.show_login()

    def show_admin_dashboard(self):
        """Afficher le dashboard administrateur"""
        self.current_dashboard = AdminDashboard()
        self.setCentralWidget(self.current_dashboard)
        self.setWindowTitle("Gestion Stock Bar-Restaurant - Dashboard Administrateur")
        self.show()

    def show_gerant_dashboard(self):
        """Afficher le dashboard gérant"""
        self.current_dashboard = GerantDashboard()
        self.setCentralWidget(self.current_dashboard)
        self.setWindowTitle("Gestion Stock Bar-Restaurant - Dashboard Gérant")
        self.show()

    def show_serveur_dashboard(self):
        """Afficher le dashboard serveur"""
        self.current_dashboard = ServeurDashboard()
        self.setCentralWidget(self.current_dashboard)
        self.setWindowTitle("Gestion Stock Bar-Restaurant - Dashboard Serveur")
        self.show()

    def logout(self):
        """Déconnecter l'utilisateur et retourner à la connexion"""
        auth_controller.logout()

        # Fermer le dashboard actuel
        if self.current_dashboard:
            self.current_dashboard = None

        # Retourner à la fenêtre de connexion
        self.show_login()

    def closeEvent(self, event):
        """Gérer la fermeture de l'application"""
        # Déconnecter l'utilisateur
        auth_controller.logout()
        event.accept()
        QApplication.quit()
