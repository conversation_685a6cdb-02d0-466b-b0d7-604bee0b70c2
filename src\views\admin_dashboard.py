# -*- coding: utf-8 -*-
"""
Dashboard pour le rôle Administrateur
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QPushButton, QFrame, QGridLayout, QMessageBox)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont
from controllers.auth_controller import auth_controller
from datetime import datetime

class AdminDashboard(QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.setup_timer()

    def init_ui(self):
        """Initialiser l'interface utilisateur"""
        # Layout principal
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # En-tête
        header_layout = self.create_header()
        main_layout.addLayout(header_layout)

        # Contenu principal
        content_layout = self.create_content()
        main_layout.addLayout(content_layout)

        self.setLayout(main_layout)

        # Style général
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                font-family: Arial, sans-serif;
            }
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
        """)

    def create_header(self):
        """Créer l'en-tête du dashboard"""
        header_layout = QHBoxLayout()

        # Informations utilisateur
        user = auth_controller.get_current_user()
        user_info = QLabel(f"Bienvenue, {user.full_name} (Administrateur)")
        user_info.setFont(QFont("Arial", 16, QFont.Bold))
        user_info.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")

        # Date et heure
        self.datetime_label = QLabel()
        self.datetime_label.setFont(QFont("Arial", 12))
        self.datetime_label.setStyleSheet("color: #6c757d;")
        self.datetime_label.setAlignment(Qt.AlignRight)

        # Bouton déconnexion
        logout_button = QPushButton("Déconnexion")
        logout_button.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                padding: 8px 16px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        logout_button.clicked.connect(self.logout)

        header_layout.addWidget(user_info)
        header_layout.addStretch()
        header_layout.addWidget(self.datetime_label)
        header_layout.addWidget(logout_button)

        return header_layout

    def create_content(self):
        """Créer le contenu principal du dashboard"""
        content_layout = QVBoxLayout()

        # Titre principal
        title = QLabel("PANNEAU D'ADMINISTRATION")
        title.setFont(QFont("Arial", 20, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #2c3e50; margin: 20px 0;")
        content_layout.addWidget(title)

        # Boutons d'action principaux
        actions_frame = QFrame()
        actions_layout = QGridLayout(actions_frame)
        actions_layout.setSpacing(15)

        # Première ligne
        user_mgmt_button = QPushButton("GESTION UTILISATEURS")
        user_mgmt_button.setMinimumHeight(60)
        user_mgmt_button.setStyleSheet("""
            QPushButton {
                background-color: #6f42c1;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
        """)
        user_mgmt_button.clicked.connect(self.manage_users)

        product_mgmt_button = QPushButton("GESTION PRODUITS")
        product_mgmt_button.setMinimumHeight(60)
        product_mgmt_button.setStyleSheet("""
            QPushButton {
                background-color: #fd7e14;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e8650e;
            }
        """)
        product_mgmt_button.clicked.connect(self.manage_products)

        stock_mgmt_button = QPushButton("GESTION STOCK")
        stock_mgmt_button.setMinimumHeight(60)
        stock_mgmt_button.setStyleSheet("""
            QPushButton {
                background-color: #20c997;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1aa179;
            }
        """)
        stock_mgmt_button.clicked.connect(self.manage_stock)

        # Deuxième ligne
        sales_button = QPushButton("VENTES")
        sales_button.setMinimumHeight(60)
        sales_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        sales_button.clicked.connect(self.manage_sales)

        expenses_button = QPushButton("DÉPENSES")
        expenses_button.setMinimumHeight(60)
        expenses_button.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        expenses_button.clicked.connect(self.manage_expenses)

        reports_button = QPushButton("RAPPORTS")
        reports_button.setMinimumHeight(60)
        reports_button.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        reports_button.clicked.connect(self.generate_reports)

        # Ajouter les boutons au layout
        actions_layout.addWidget(user_mgmt_button, 0, 0)
        actions_layout.addWidget(product_mgmt_button, 0, 1)
        actions_layout.addWidget(stock_mgmt_button, 0, 2)
        actions_layout.addWidget(sales_button, 1, 0)
        actions_layout.addWidget(expenses_button, 1, 1)
        actions_layout.addWidget(reports_button, 1, 2)

        content_layout.addWidget(actions_frame)
        content_layout.addStretch()

        return content_layout

    def setup_timer(self):
        """Configurer le timer pour la mise à jour de l'heure"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_datetime)
        self.timer.start(1000)  # Mise à jour chaque seconde
        self.update_datetime()

    def update_datetime(self):
        """Mettre à jour l'affichage de la date et heure"""
        now = datetime.now()
        self.datetime_label.setText(now.strftime("%d/%m/%Y %H:%M:%S"))

    def manage_users(self):
        """Ouvrir la gestion des utilisateurs"""
        QMessageBox.information(self, "Gestion Utilisateurs",
                               "Fonctionnalité de gestion des utilisateurs en cours de développement...")

    def manage_products(self):
        """Ouvrir la gestion des produits"""
        QMessageBox.information(self, "Gestion Produits",
                               "Fonctionnalité de gestion des produits en cours de développement...")

    def manage_stock(self):
        """Ouvrir la gestion du stock"""
        QMessageBox.information(self, "Gestion Stock",
                               "Fonctionnalité de gestion du stock en cours de développement...")

    def manage_sales(self):
        """Ouvrir la gestion des ventes"""
        QMessageBox.information(self, "Gestion Ventes",
                               "Fonctionnalité de gestion des ventes en cours de développement...")

    def manage_expenses(self):
        """Ouvrir la gestion des dépenses"""
        QMessageBox.information(self, "Gestion Dépenses",
                               "Fonctionnalité de gestion des dépenses en cours de développement...")

    def generate_reports(self):
        """Ouvrir la génération de rapports"""
        QMessageBox.information(self, "Génération Rapports",
                               "Fonctionnalité de génération de rapports en cours de développement...")

    def logout(self):
        """Déconnecter l'utilisateur"""
        reply = QMessageBox.question(
            self,
            'Déconnexion',
            'Êtes-vous sûr de vouloir vous déconnecter?',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Obtenir la fenêtre principale et déclencher la déconnexion
            main_window = self.window()
            if hasattr(main_window, 'logout'):
                main_window.logout()
