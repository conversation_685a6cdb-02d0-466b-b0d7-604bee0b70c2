# -*- coding: utf-8 -*-
"""
Fenêtre de connexion
"""

import sys
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QLineEdit, QPushButton, QMessageBox, QFrame,
                             QApplication, QGridLayout, QSpacerItem, QSizePolicy)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QIcon
from controllers.auth_controller import auth_controller

class LoginWindow(QWidget):
    # Signal émis lors d'une connexion réussie
    login_successful = pyqtSignal(dict)

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        """Initialiser l'interface utilisateur"""
        self.setWindowTitle("Gestion Stock Bar-Restaurant - Connexion")
        self.setFixedSize(400, 300)
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint)

        # Centrer la fenêtre
        self.center_window()

        # Layout principal
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(40, 40, 40, 40)

        # Titre
        title_label = QLabel("CONNEXION")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 20px;")

        # Frame pour le formulaire
        form_frame = QFrame()
        form_frame.setFrameStyle(QFrame.StyledPanel)
        form_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 20px;
            }
        """)

        # Layout du formulaire
        form_layout = QGridLayout(form_frame)
        form_layout.setSpacing(15)

        # Champ nom d'utilisateur
        username_label = QLabel("Nom d'utilisateur:")
        username_label.setFont(QFont("Arial", 10))
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("Entrez votre nom d'utilisateur")
        self.username_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #007bff;
            }
        """)

        # Champ mot de passe
        password_label = QLabel("Mot de passe:")
        password_label.setFont(QFont("Arial", 10))
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setPlaceholderText("Entrez votre mot de passe")
        self.password_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #007bff;
            }
        """)

        # Ajouter les champs au layout
        form_layout.addWidget(username_label, 0, 0)
        form_layout.addWidget(self.username_input, 0, 1)
        form_layout.addWidget(password_label, 1, 0)
        form_layout.addWidget(self.password_input, 1, 1)

        # Boutons
        button_layout = QHBoxLayout()

        self.login_button = QPushButton("Se connecter")
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
        """)

        self.cancel_button = QPushButton("Annuler")
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
        """)

        button_layout.addWidget(self.login_button)
        button_layout.addWidget(self.cancel_button)

        # Ajouter les éléments au layout principal
        main_layout.addWidget(title_label)
        main_layout.addWidget(form_frame)
        main_layout.addLayout(button_layout)

        # Spacer pour centrer verticalement
        main_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))

        self.setLayout(main_layout)

        # Connecter les signaux
        self.login_button.clicked.connect(self.handle_login)
        self.cancel_button.clicked.connect(self.close)
        self.password_input.returnPressed.connect(self.handle_login)
        self.username_input.returnPressed.connect(self.handle_login)

        # Focus sur le champ nom d'utilisateur
        self.username_input.setFocus()

    def center_window(self):
        """Centrer la fenêtre sur l'écran"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )

    def handle_login(self):
        """Gérer la tentative de connexion"""
        username = self.username_input.text().strip()
        password = self.password_input.text()

        # Validation des champs
        if not username:
            QMessageBox.warning(self, "Erreur", "Veuillez entrer votre nom d'utilisateur.")
            self.username_input.setFocus()
            return

        if not password:
            QMessageBox.warning(self, "Erreur", "Veuillez entrer votre mot de passe.")
            self.password_input.setFocus()
            return

        # Désactiver le bouton pendant la connexion
        self.login_button.setEnabled(False)
        self.login_button.setText("Connexion...")

        # Tentative de connexion
        result = auth_controller.login(username, password)

        # Réactiver le bouton
        self.login_button.setEnabled(True)
        self.login_button.setText("Se connecter")

        if result['success']:
            # Connexion réussie
            QMessageBox.information(self, "Succès", result['message'])
            self.login_successful.emit(result['user'])
            self.close()
        else:
            # Échec de la connexion
            QMessageBox.critical(self, "Erreur de connexion", result['message'])
            self.password_input.clear()
            self.password_input.setFocus()

    def closeEvent(self, event):
        """Gérer la fermeture de la fenêtre"""
        reply = QMessageBox.question(
            self,
            'Fermer l\'application',
            'Êtes-vous sûr de vouloir fermer l\'application?',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            event.accept()
            QApplication.quit()
        else:
            event.ignore()
