# -*- coding: utf-8 -*-
"""
Configuration de l'application
"""

import os

# Chemins de base
BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
DATA_DIR = os.path.join(BASE_DIR, 'data')
REPORTS_DIR = os.path.join(BASE_DIR, 'reports')
ASSETS_DIR = os.path.join(BASE_DIR, 'assets')

# Base de données
DATABASE_URL = f"sqlite:///{os.path.join(DATA_DIR, 'bar_restaurant.db')}"

# Configuration de l'application
APP_CONFIG = {
    'name': 'Gestion Stock Bar-Restaurant',
    'version': '1.0.0',
    'author': 'Alain Dev',
    'description': 'Application de gestion de stock pour bar-restaurant au Burundi'
}

# Rôles utilisateurs
USER_ROLES = {
    'ADMIN': 'admin',
    'GERANT': 'gerant',
    'SERVEUR': 'serveur'
}

# Permissions par rôle
PERMISSIONS = {
    'admin': [
        'login', 'create_user', 'modify_user', 'add_product', 'modify_product',
        'make_sale', 'view_sales_history', 'add_supply', 'view_stock',
        'generate_reports', 'record_expense', 'delete_sale', 'delete_product',
        'manage_database'
    ],
    'gerant': [
        'login', 'add_product', 'modify_product', 'make_sale', 'view_sales_history',
        'add_supply', 'view_stock', 'generate_reports', 'record_expense'
    ],
    'serveur': [
        'login', 'make_sale'
    ]
}

# Catégories de produits
PRODUCT_CATEGORIES = [
    'Boissons',
    'Plats',
    'Snacks'
]

# Seuil d'alerte stock bas
LOW_STOCK_THRESHOLD = 10

# Configuration des rapports
REPORT_CONFIG = {
    'company_name': 'Bar-Restaurant',
    'address': 'Burundi',
    'phone': '+257 XX XX XX XX',
    'email': '<EMAIL>'
}
