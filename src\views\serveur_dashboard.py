# -*- coding: utf-8 -*-
"""
Dashboard pour le rôle Serveur
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QPushButton, QFrame, QGridLayout, QMessageBox)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QIcon
from controllers.auth_controller import auth_controller
from datetime import datetime

class ServeurDashboard(QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.setup_timer()

    def init_ui(self):
        """Initialiser l'interface utilisateur"""
        # Layout principal
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # En-tête
        header_layout = self.create_header()
        main_layout.addLayout(header_layout)

        # Contenu principal
        content_layout = self.create_content()
        main_layout.addLayout(content_layout)

        self.setLayout(main_layout)

        # Style général
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                font-family: Arial, sans-serif;
            }
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
        """)

    def create_header(self):
        """Créer l'en-tête du dashboard"""
        header_layout = QHBoxLayout()

        # Informations utilisateur
        user = auth_controller.get_current_user()
        user_info = QLabel(f"Bienvenue, {user.full_name} (Serveur)")
        user_info.setFont(QFont("Arial", 16, QFont.Bold))
        user_info.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")

        # Date et heure
        self.datetime_label = QLabel()
        self.datetime_label.setFont(QFont("Arial", 12))
        self.datetime_label.setStyleSheet("color: #6c757d;")
        self.datetime_label.setAlignment(Qt.AlignRight)

        # Bouton déconnexion
        logout_button = QPushButton("Déconnexion")
        logout_button.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                padding: 8px 16px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        logout_button.clicked.connect(self.logout)

        header_layout.addWidget(user_info)
        header_layout.addStretch()
        header_layout.addWidget(self.datetime_label)
        header_layout.addWidget(logout_button)

        return header_layout

    def create_content(self):
        """Créer le contenu principal du dashboard"""
        content_layout = QVBoxLayout()

        # Titre principal
        title = QLabel("POINT DE VENTE")
        title.setFont(QFont("Arial", 20, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #2c3e50; margin: 20px 0;")
        content_layout.addWidget(title)

        # Boutons d'action principaux
        actions_frame = QFrame()
        actions_layout = QGridLayout(actions_frame)
        actions_layout.setSpacing(20)

        # Bouton Nouvelle Vente
        new_sale_button = QPushButton("NOUVELLE VENTE")
        new_sale_button.setMinimumHeight(80)
        new_sale_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                font-size: 18px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        new_sale_button.clicked.connect(self.new_sale)

        # Bouton Historique des Ventes (lecture seule)
        sales_history_button = QPushButton("HISTORIQUE DES VENTES")
        sales_history_button.setMinimumHeight(80)
        sales_history_button.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                font-size: 18px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        sales_history_button.clicked.connect(self.view_sales_history)

        actions_layout.addWidget(new_sale_button, 0, 0)
        actions_layout.addWidget(sales_history_button, 0, 1)

        content_layout.addWidget(actions_frame)

        # Informations rapides
        info_frame = self.create_info_section()
        content_layout.addWidget(info_frame)

        content_layout.addStretch()

        return content_layout

    def create_info_section(self):
        """Créer la section d'informations rapides"""
        info_frame = QFrame()
        info_layout = QVBoxLayout(info_frame)

        info_title = QLabel("Informations")
        info_title.setFont(QFont("Arial", 14, QFont.Bold))
        info_title.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")

        info_text = QLabel("""
        • Vous pouvez effectuer des ventes et consulter l'historique
        • Pour toute modification ou annulation, contactez votre gérant
        • Vérifiez toujours les prix avant de finaliser une vente
        """)
        info_text.setStyleSheet("color: #6c757d; line-height: 1.5;")
        info_text.setWordWrap(True)

        info_layout.addWidget(info_title)
        info_layout.addWidget(info_text)

        return info_frame

    def setup_timer(self):
        """Configurer le timer pour la mise à jour de l'heure"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_datetime)
        self.timer.start(1000)  # Mise à jour chaque seconde
        self.update_datetime()

    def update_datetime(self):
        """Mettre à jour l'affichage de la date et heure"""
        now = datetime.now()
        self.datetime_label.setText(now.strftime("%d/%m/%Y %H:%M:%S"))

    def new_sale(self):
        """Ouvrir la fenêtre de nouvelle vente"""
        QMessageBox.information(self, "Nouvelle Vente",
                               "Fonctionnalité de vente en cours de développement...")

    def view_sales_history(self):
        """Afficher l'historique des ventes"""
        QMessageBox.information(self, "Historique des Ventes",
                               "Fonctionnalité d'historique en cours de développement...")

    def logout(self):
        """Déconnecter l'utilisateur"""
        reply = QMessageBox.question(
            self,
            'Déconnexion',
            'Êtes-vous sûr de vouloir vous déconnecter?',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Obtenir la fenêtre principale et déclencher la déconnexion
            main_window = self.window()
            if hasattr(main_window, 'logout'):
                main_window.logout()
