# -*- coding: utf-8 -*-
"""
Modèles Sale et SaleItem pour la gestion des ventes
"""

from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from models.database import Base

class Sale(Base):
    __tablename__ = 'sales'

    id = Column(Integer, primary_key=True, index=True)
    sale_number = Column(String(50), unique=True, nullable=False, index=True)  # Numéro de vente
    table_number = Column(String(20), nullable=True)  # Numéro de table (optionnel)
    customer_name = Column(String(100), nullable=True)  # Nom du client (optionnel)
    total_amount = Column(Float, nullable=False, default=0.0)  # Montant total
    discount = Column(Float, nullable=False, default=0.0)  # Remise
    tax_amount = Column(Float, nullable=False, default=0.0)  # Montant de la taxe
    final_amount = Column(Float, nullable=False, default=0.0)  # Montant final
    payment_method = Column(String(20), nullable=False, default='Espèces')  # Mode de paiement
    status = Column(String(20), nullable=False, default='Terminée')  # Statut (En cours, Terminée, Annulée)
    notes = Column(Text, nullable=True)  # Notes additionnelles
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)  # Utilisateur qui a fait la vente
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relations
    user = relationship("User", backref="sales")
    items = relationship("SaleItem", back_populates="sale", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Sale(number='{self.sale_number}', total={self.final_amount}, status='{self.status}')>"

    def calculate_totals(self):
        """Calculer les totaux de la vente"""
        self.total_amount = sum(item.subtotal for item in self.items)
        self.final_amount = self.total_amount - self.discount + self.tax_amount

    def to_dict(self):
        """Convertir l'objet en dictionnaire"""
        return {
            'id': self.id,
            'sale_number': self.sale_number,
            'table_number': self.table_number,
            'customer_name': self.customer_name,
            'total_amount': self.total_amount,
            'discount': self.discount,
            'tax_amount': self.tax_amount,
            'final_amount': self.final_amount,
            'payment_method': self.payment_method,
            'status': self.status,
            'notes': self.notes,
            'user_id': self.user_id,
            'user_name': self.user.full_name if self.user else None,
            'items_count': len(self.items),
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class SaleItem(Base):
    __tablename__ = 'sale_items'

    id = Column(Integer, primary_key=True, index=True)
    sale_id = Column(Integer, ForeignKey('sales.id'), nullable=False)
    product_id = Column(Integer, ForeignKey('products.id'), nullable=False)
    product_name = Column(String(100), nullable=False)  # Nom du produit au moment de la vente
    quantity = Column(Integer, nullable=False)
    unit_price = Column(Float, nullable=False)  # Prix unitaire au moment de la vente
    subtotal = Column(Float, nullable=False)  # Sous-total (quantity * unit_price)

    # Relations
    sale = relationship("Sale", back_populates="items")
    product = relationship("Product", backref="sale_items")

    def __repr__(self):
        return f"<SaleItem(product='{self.product_name}', qty={self.quantity}, subtotal={self.subtotal})>"

    def calculate_subtotal(self):
        """Calculer le sous-total"""
        self.subtotal = self.quantity * self.unit_price

    def to_dict(self):
        """Convertir l'objet en dictionnaire"""
        return {
            'id': self.id,
            'sale_id': self.sale_id,
            'product_id': self.product_id,
            'product_name': self.product_name,
            'quantity': self.quantity,
            'unit_price': self.unit_price,
            'subtotal': self.subtotal
        }
