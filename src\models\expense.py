# -*- coding: utf-8 -*-
"""
Modèle Expense pour la gestion des dépenses
"""

from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from models.database import Base

class Expense(Base):
    __tablename__ = 'expenses'

    id = Column(Integer, primary_key=True, index=True)
    expense_number = Column(String(50), unique=True, nullable=False, index=True)  # Numéro de dépense
    category = Column(String(50), nullable=False)  # Catégorie (Gaz, Salaires, Électricité, etc.)
    description = Column(String(200), nullable=False)  # Description de la dépense
    amount = Column(Float, nullable=False)  # Montant de la dépense
    payment_method = Column(String(20), nullable=False, default='Espèces')  # Mode de paiement
    recipient = Column(String(100), nullable=True)  # Bénéficiaire/Fournisseur
    receipt_number = Column(String(50), nullable=True)  # Numéro de reçu/facture
    notes = Column(Text, nullable=True)  # Notes additionnelles
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)  # Utilisateur qui a enregistré la dépense
    expense_date = Column(DateTime, nullable=False, default=func.now())  # Date de la dépense
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relations
    user = relationship("User", backref="expenses")

    def __repr__(self):
        return f"<Expense(number='{self.expense_number}', category='{self.category}', amount={self.amount})>"

    def to_dict(self):
        """Convertir l'objet en dictionnaire"""
        return {
            'id': self.id,
            'expense_number': self.expense_number,
            'category': self.category,
            'description': self.description,
            'amount': self.amount,
            'payment_method': self.payment_method,
            'recipient': self.recipient,
            'receipt_number': self.receipt_number,
            'notes': self.notes,
            'user_id': self.user_id,
            'user_name': self.user.full_name if self.user else None,
            'expense_date': self.expense_date.isoformat() if self.expense_date else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
