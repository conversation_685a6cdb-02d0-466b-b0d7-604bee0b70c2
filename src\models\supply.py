# -*- coding: utf-8 -*-
"""
Modèle Supply pour la gestion des approvisionnements
"""

from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from models.database import Base

class Supply(Base):
    __tablename__ = 'supplies'

    id = Column(Integer, primary_key=True, index=True)
    supply_number = Column(String(50), unique=True, nullable=False, index=True)  # Numéro d'approvisionnement
    supplier_name = Column(String(100), nullable=False)  # Nom du fournisseur
    supplier_contact = Column(String(100), nullable=True)  # Contact du fournisseur
    product_id = Column(Integer, ForeignKey('products.id'), nullable=False)
    product_name = Column(String(100), nullable=False)  # Nom du produit au moment de l'approvisionnement
    quantity = Column(Integer, nullable=False)  # Quantité approvisionnée
    unit_cost = Column(Float, nullable=False)  # Coût unitaire
    total_cost = Column(Float, nullable=False)  # Coût total
    expiry_date = Column(DateTime, nullable=True)  # Date d'expiration (optionnel)
    notes = Column(Text, nullable=True)  # Notes additionnelles
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)  # Utilisateur qui a fait l'approvisionnement
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relations
    user = relationship("User", backref="supplies")
    product = relationship("Product", backref="supplies")

    def __repr__(self):
        return f"<Supply(number='{self.supply_number}', product='{self.product_name}', qty={self.quantity})>"

    def calculate_total_cost(self):
        """Calculer le coût total"""
        self.total_cost = self.quantity * self.unit_cost

    def to_dict(self):
        """Convertir l'objet en dictionnaire"""
        return {
            'id': self.id,
            'supply_number': self.supply_number,
            'supplier_name': self.supplier_name,
            'supplier_contact': self.supplier_contact,
            'product_id': self.product_id,
            'product_name': self.product_name,
            'quantity': self.quantity,
            'unit_cost': self.unit_cost,
            'total_cost': self.total_cost,
            'expiry_date': self.expiry_date.isoformat() if self.expiry_date else None,
            'notes': self.notes,
            'user_id': self.user_id,
            'user_name': self.user.full_name if self.user else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
