# -*- coding: utf-8 -*-
"""
Fenêtre de gestion des ventes
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QTableWidget, QTableWidgetItem, 
                             QLineEdit, QComboBox, QDateEdit, QDoubleSpinBox,
                             QMessageBox, QHeaderView, QFrame, QFormLayout,
                             QGroupBox, QGridLayout, QSpinBox, QTextEdit)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont
from models.database import SessionLocal
from models.sale import Sale, SaleItem
from models.product import Product
from controllers.auth_controller import auth_controller
from datetime import datetime, date

class SalesManagement(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db = SessionLocal()
        self.init_ui()
        self.load_sales()
        
    def init_ui(self):
        """Initialiser l'interface utilisateur"""
        self.setWindowTitle("Gestion des Ventes")
        self.setMinimumSize(1200, 800)
        self.setModal(True)
        
        # Layout principal
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # En-tête
        header = QLabel("GESTION DES VENTES")
        header.setFont(QFont("Arial", 18, QFont.Bold))
        header.setAlignment(Qt.AlignCenter)
        header.setStyleSheet("color: #2c3e50; margin-bottom: 20px;")
        main_layout.addWidget(header)
        
        # Section de recherche et filtres
        search_layout = self.create_search_section()
        main_layout.addLayout(search_layout)
        
        # Tableau des ventes
        self.create_sales_table()
        main_layout.addWidget(self.sales_table)
        
        # Boutons d'action
        buttons_layout = self.create_buttons_section()
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
        # Style général
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                font-family: Arial, sans-serif;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QTableWidget {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                gridline-color: #dee2e6;
            }
            QTableWidget::item {
                padding: 8px;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
            QLineEdit, QComboBox, QDateEdit {
                padding: 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: white;
            }
        """)
    
    def create_search_section(self):
        """Créer la section de recherche et filtres"""
        search_layout = QHBoxLayout()
        
        # Filtre par date
        date_label = QLabel("Date:")
        self.date_filter = QDateEdit()
        self.date_filter.setDate(QDate.currentDate())
        self.date_filter.setCalendarPopup(True)
        self.date_filter.dateChanged.connect(self.filter_sales)
        
        # Filtre par vendeur
        seller_label = QLabel("Vendeur:")
        self.seller_filter = QComboBox()
        self.seller_filter.addItem("Tous les vendeurs")
        self.load_sellers()
        self.seller_filter.currentTextChanged.connect(self.filter_sales)
        
        # Recherche par numéro de vente
        search_label = QLabel("N° Vente:")
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Numéro de vente...")
        self.search_input.textChanged.connect(self.filter_sales)
        
        search_layout.addWidget(date_label)
        search_layout.addWidget(self.date_filter)
        search_layout.addWidget(seller_label)
        search_layout.addWidget(self.seller_filter)
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        search_layout.addStretch()
        
        return search_layout
    
    def create_sales_table(self):
        """Créer le tableau des ventes"""
        self.sales_table = QTableWidget()
        self.sales_table.setColumnCount(7)
        self.sales_table.setHorizontalHeaderLabels([
            "N° Vente", "Date", "Heure", "Vendeur", 
            "Montant Total", "Remise", "Montant Final"
        ])
        
        # Configuration du tableau
        header = self.sales_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # N° Vente
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Date
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Heure
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # Vendeur
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Montant Total
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Remise
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Montant Final
        
        self.sales_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.sales_table.setAlternatingRowColors(True)
        self.sales_table.doubleClicked.connect(self.view_sale_details)
    
    def create_buttons_section(self):
        """Créer la section des boutons"""
        buttons_layout = QHBoxLayout()
        
        # Bouton Nouvelle vente
        new_button = QPushButton("Nouvelle Vente")
        new_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                font-size: 14px;
                padding: 12px 24px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        new_button.clicked.connect(self.new_sale)
        
        # Bouton Voir détails
        details_button = QPushButton("Voir Détails")
        details_button.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                font-size: 14px;
                padding: 12px 24px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        details_button.clicked.connect(self.view_sale_details)
        
        # Bouton Actualiser
        refresh_button = QPushButton("Actualiser")
        refresh_button.clicked.connect(self.load_sales)
        
        # Bouton Fermer
        close_button = QPushButton("Fermer")
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                font-size: 14px;
                padding: 12px 24px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        close_button.clicked.connect(self.close)
        
        buttons_layout.addWidget(new_button)
        buttons_layout.addWidget(details_button)
        buttons_layout.addStretch()
        buttons_layout.addWidget(refresh_button)
        buttons_layout.addWidget(close_button)
        
        return buttons_layout
    
    def load_sellers(self):
        """Charger la liste des vendeurs"""
        try:
            from models.user import User
            sellers = self.db.query(User).filter(User.is_active == True).all()
            for seller in sellers:
                self.seller_filter.addItem(seller.full_name)
        except Exception as e:
            print(f"Erreur lors du chargement des vendeurs: {e}")
    
    def load_sales(self):
        """Charger les ventes dans le tableau"""
        try:
            sales = self.db.query(Sale).order_by(Sale.sale_date.desc()).all()
            self.sales_table.setRowCount(len(sales))
            
            for row, sale in enumerate(sales):
                # N° Vente
                self.sales_table.setItem(row, 0, QTableWidgetItem(str(sale.id)))
                
                # Date
                sale_date = sale.sale_date.strftime("%d/%m/%Y")
                self.sales_table.setItem(row, 1, QTableWidgetItem(sale_date))
                
                # Heure
                sale_time = sale.sale_date.strftime("%H:%M")
                self.sales_table.setItem(row, 2, QTableWidgetItem(sale_time))
                
                # Vendeur
                seller_name = sale.seller.full_name if sale.seller else "N/A"
                self.sales_table.setItem(row, 3, QTableWidgetItem(seller_name))
                
                # Montant Total
                self.sales_table.setItem(row, 4, QTableWidgetItem(f"{sale.total_amount:.0f} FBU"))
                
                # Remise
                self.sales_table.setItem(row, 5, QTableWidgetItem(f"{sale.discount:.0f} FBU"))
                
                # Montant Final
                self.sales_table.setItem(row, 6, QTableWidgetItem(f"{sale.final_amount:.0f} FBU"))
                
                # Stocker l'ID de la vente
                self.sales_table.item(row, 0).setData(Qt.UserRole, sale.id)
                
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des ventes: {e}")
    
    def filter_sales(self):
        """Filtrer les ventes selon les critères"""
        selected_date = self.date_filter.date().toPyDate()
        seller_filter = self.seller_filter.currentText()
        search_text = self.search_input.text().lower()
        
        for row in range(self.sales_table.rowCount()):
            show_row = True
            
            # Filtre par date
            date_item = self.sales_table.item(row, 1)
            if date_item:
                row_date = datetime.strptime(date_item.text(), "%d/%m/%Y").date()
                if row_date != selected_date:
                    show_row = False
            
            # Filtre par vendeur
            if seller_filter != "Tous les vendeurs":
                seller_item = self.sales_table.item(row, 3)
                if seller_item and seller_item.text() != seller_filter:
                    show_row = False
            
            # Filtre par numéro de vente
            if search_text:
                sale_number = self.sales_table.item(row, 0).text().lower()
                if search_text not in sale_number:
                    show_row = False
            
            self.sales_table.setRowHidden(row, not show_row)
    
    def new_sale(self):
        """Créer une nouvelle vente"""
        dialog = NewSaleDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_sales()
    
    def view_sale_details(self):
        """Voir les détails de la vente sélectionnée"""
        current_row = self.sales_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "Attention", "Veuillez sélectionner une vente.")
            return
        
        sale_id = self.sales_table.item(current_row, 0).data(Qt.UserRole)
        sale = self.db.query(Sale).filter(Sale.id == sale_id).first()
        
        if sale:
            dialog = SaleDetailsDialog(self, sale)
            dialog.exec_()
    
    def closeEvent(self, event):
        """Fermer la session de base de données"""
        self.db.close()
        event.accept()


class NewSaleDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db = SessionLocal()
        self.sale_items = []
        self.init_ui()
        self.load_products()
        
    def init_ui(self):
        """Initialiser l'interface du dialogue"""
        self.setWindowTitle("Nouvelle Vente")
        self.setMinimumSize(800, 600)
        self.setModal(True)
        
        # Layout principal
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # En-tête
        header = QLabel("NOUVELLE VENTE")
        header.setFont(QFont("Arial", 16, QFont.Bold))
        header.setAlignment(Qt.AlignCenter)
        header.setStyleSheet("color: #2c3e50; margin-bottom: 20px;")
        main_layout.addWidget(header)
        
        # Section d'ajout de produits
        product_section = self.create_product_section()
        main_layout.addLayout(product_section)
        
        # Tableau des articles
        self.create_items_table()
        main_layout.addWidget(self.items_table)
        
        # Section totaux
        totals_section = self.create_totals_section()
        main_layout.addLayout(totals_section)
        
        # Boutons
        buttons_layout = self.create_dialog_buttons()
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
        # Style
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                font-family: Arial, sans-serif;
            }
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox {
                padding: 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: white;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QTableWidget {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 5px;
            }
        """)
    
    def create_product_section(self):
        """Créer la section d'ajout de produits"""
        layout = QHBoxLayout()
        
        # Sélection du produit
        product_label = QLabel("Produit:")
        self.product_combo = QComboBox()
        self.product_combo.currentTextChanged.connect(self.update_product_info)
        
        # Quantité
        qty_label = QLabel("Quantité:")
        self.quantity_input = QSpinBox()
        self.quantity_input.setRange(1, 999)
        self.quantity_input.setValue(1)
        
        # Prix unitaire (affiché)
        price_label = QLabel("Prix unitaire:")
        self.price_display = QLabel("0 FBU")
        self.price_display.setStyleSheet("font-weight: bold; color: #28a745;")
        
        # Bouton ajouter
        add_button = QPushButton("Ajouter")
        add_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        add_button.clicked.connect(self.add_item)
        
        layout.addWidget(product_label)
        layout.addWidget(self.product_combo)
        layout.addWidget(qty_label)
        layout.addWidget(self.quantity_input)
        layout.addWidget(price_label)
        layout.addWidget(self.price_display)
        layout.addWidget(add_button)
        layout.addStretch()
        
        return layout
    
    def create_items_table(self):
        """Créer le tableau des articles"""
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(5)
        self.items_table.setHorizontalHeaderLabels([
            "Produit", "Prix Unitaire", "Quantité", "Sous-total", "Action"
        ])
        
        # Configuration du tableau
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        
        self.items_table.setAlternatingRowColors(True)
    
    def create_totals_section(self):
        """Créer la section des totaux"""
        layout = QHBoxLayout()
        
        # Remise
        discount_label = QLabel("Remise:")
        self.discount_input = QDoubleSpinBox()
        self.discount_input.setRange(0, 999999)
        self.discount_input.setSuffix(" FBU")
        self.discount_input.valueChanged.connect(self.calculate_totals)
        
        # Total
        self.total_label = QLabel("TOTAL: 0 FBU")
        self.total_label.setFont(QFont("Arial", 14, QFont.Bold))
        self.total_label.setStyleSheet("color: #dc3545; padding: 10px; border: 2px solid #dc3545; border-radius: 5px;")
        
        layout.addWidget(discount_label)
        layout.addWidget(self.discount_input)
        layout.addStretch()
        layout.addWidget(self.total_label)
        
        return layout
    
    def create_dialog_buttons(self):
        """Créer les boutons du dialogue"""
        buttons_layout = QHBoxLayout()
        
        # Bouton Enregistrer
        save_button = QPushButton("Enregistrer la Vente")
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                font-size: 14px;
                padding: 12px 24px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        save_button.clicked.connect(self.save_sale)
        
        # Bouton Annuler
        cancel_button = QPushButton("Annuler")
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                font-size: 14px;
                padding: 12px 24px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        cancel_button.clicked.connect(self.reject)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(cancel_button)
        
        return buttons_layout
    
    def load_products(self):
        """Charger les produits disponibles"""
        try:
            products = self.db.query(Product).filter(
                Product.is_active == "Oui",
                Product.current_stock > 0
            ).all()
            
            self.product_combo.clear()
            self.product_combo.addItem("Sélectionner un produit", None)
            
            for product in products:
                display_text = f"{product.name} (Stock: {product.current_stock})"
                self.product_combo.addItem(display_text, product)
                
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des produits: {e}")
    
    def update_product_info(self):
        """Mettre à jour les informations du produit sélectionné"""
        product = self.product_combo.currentData()
        if product:
            self.price_display.setText(f"{product.selling_price:.0f} FBU")
            self.quantity_input.setMaximum(product.current_stock)
        else:
            self.price_display.setText("0 FBU")
            self.quantity_input.setMaximum(999)
    
    def add_item(self):
        """Ajouter un article à la vente"""
        product = self.product_combo.currentData()
        if not product:
            QMessageBox.warning(self, "Attention", "Veuillez sélectionner un produit.")
            return
        
        quantity = self.quantity_input.value()
        if quantity > product.current_stock:
            QMessageBox.warning(self, "Attention", f"Stock insuffisant. Stock disponible: {product.current_stock}")
            return
        
        # Vérifier si le produit est déjà dans la liste
        for i, item in enumerate(self.sale_items):
            if item['product'].id == product.id:
                # Mettre à jour la quantité
                new_quantity = item['quantity'] + quantity
                if new_quantity > product.current_stock:
                    QMessageBox.warning(self, "Attention", f"Stock insuffisant. Stock disponible: {product.current_stock}")
                    return
                self.sale_items[i]['quantity'] = new_quantity
                self.sale_items[i]['subtotal'] = new_quantity * product.selling_price
                self.update_items_table()
                self.calculate_totals()
                return
        
        # Ajouter un nouvel article
        item = {
            'product': product,
            'unit_price': product.selling_price,
            'quantity': quantity,
            'subtotal': quantity * product.selling_price
        }
        self.sale_items.append(item)
        self.update_items_table()
        self.calculate_totals()
    
    def update_items_table(self):
        """Mettre à jour le tableau des articles"""
        self.items_table.setRowCount(len(self.sale_items))
        
        for row, item in enumerate(self.sale_items):
            # Produit
            self.items_table.setItem(row, 0, QTableWidgetItem(item['product'].name))
            
            # Prix unitaire
            self.items_table.setItem(row, 1, QTableWidgetItem(f"{item['unit_price']:.0f} FBU"))
            
            # Quantité
            self.items_table.setItem(row, 2, QTableWidgetItem(str(item['quantity'])))
            
            # Sous-total
            self.items_table.setItem(row, 3, QTableWidgetItem(f"{item['subtotal']:.0f} FBU"))
            
            # Bouton supprimer
            remove_button = QPushButton("Supprimer")
            remove_button.setStyleSheet("""
                QPushButton {
                    background-color: #dc3545;
                    font-size: 12px;
                    padding: 5px 10px;
                }
                QPushButton:hover {
                    background-color: #c82333;
                }
            """)
            remove_button.clicked.connect(lambda checked, r=row: self.remove_item(r))
            self.items_table.setCellWidget(row, 4, remove_button)
    
    def remove_item(self, row):
        """Supprimer un article de la vente"""
        if 0 <= row < len(self.sale_items):
            self.sale_items.pop(row)
            self.update_items_table()
            self.calculate_totals()
    
    def calculate_totals(self):
        """Calculer les totaux"""
        subtotal = sum(item['subtotal'] for item in self.sale_items)
        discount = self.discount_input.value()
        total = subtotal - discount
        
        self.total_label.setText(f"TOTAL: {total:.0f} FBU")
    
    def save_sale(self):
        """Enregistrer la vente"""
        if not self.sale_items:
            QMessageBox.warning(self, "Attention", "Veuillez ajouter au moins un article.")
            return
        
        try:
            # Créer la vente
            current_user = auth_controller.get_current_user()
            sale = Sale(
                seller_id=current_user.id,
                sale_date=datetime.now(),
                discount=self.discount_input.value()
            )
            
            # Ajouter les articles
            for item_data in self.sale_items:
                sale_item = SaleItem(
                    product_id=item_data['product'].id,
                    quantity=item_data['quantity'],
                    unit_price=item_data['unit_price'],
                    subtotal=item_data['subtotal']
                )
                sale.items.append(sale_item)
                
                # Mettre à jour le stock
                product = item_data['product']
                product.update_stock(item_data['quantity'], 'subtract')
            
            # Calculer les totaux
            sale.calculate_totals()
            
            self.db.add(sale)
            self.db.commit()
            
            QMessageBox.information(self, "Succès", f"Vente enregistrée avec succès!\nN° de vente: {sale.id}")
            self.accept()
            
        except Exception as e:
            self.db.rollback()
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement: {e}")
    
    def closeEvent(self, event):
        """Fermer la session de base de données"""
        self.db.close()
        event.accept()


class SaleDetailsDialog(QDialog):
    def __init__(self, parent=None, sale=None):
        super().__init__(parent)
        self.sale = sale
        self.init_ui()
        self.load_sale_details()
        
    def init_ui(self):
        """Initialiser l'interface du dialogue"""
        self.setWindowTitle(f"Détails de la Vente N° {self.sale.id}")
        self.setMinimumSize(600, 500)
        self.setModal(True)
        
        # Layout principal
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # En-tête avec informations de la vente
        header_layout = self.create_header_section()
        main_layout.addLayout(header_layout)
        
        # Tableau des articles
        self.create_details_table()
        main_layout.addWidget(self.details_table)
        
        # Section totaux
        totals_layout = self.create_totals_section()
        main_layout.addLayout(totals_layout)
        
        # Bouton fermer
        close_button = QPushButton("Fermer")
        close_button.clicked.connect(self.close)
        close_layout = QHBoxLayout()
        close_layout.addStretch()
        close_layout.addWidget(close_button)
        main_layout.addLayout(close_layout)
        
        self.setLayout(main_layout)
        
        # Style
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                font-family: Arial, sans-serif;
            }
            QLabel {
                color: #495057;
            }
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
            QTableWidget {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 5px;
            }
        """)
    
    def create_header_section(self):
        """Créer la section d'en-tête avec les informations de la vente"""
        layout = QGridLayout()
        
        # Informations de la vente
        layout.addWidget(QLabel("N° de Vente:"), 0, 0)
        layout.addWidget(QLabel(str(self.sale.id)), 0, 1)
        
        layout.addWidget(QLabel("Date:"), 0, 2)
        layout.addWidget(QLabel(self.sale.sale_date.strftime("%d/%m/%Y")), 0, 3)
        
        layout.addWidget(QLabel("Heure:"), 1, 0)
        layout.addWidget(QLabel(self.sale.sale_date.strftime("%H:%M:%S")), 1, 1)
        
        layout.addWidget(QLabel("Vendeur:"), 1, 2)
        seller_name = self.sale.seller.full_name if self.sale.seller else "N/A"
        layout.addWidget(QLabel(seller_name), 1, 3)
        
        return layout
    
    def create_details_table(self):
        """Créer le tableau des détails"""
        self.details_table = QTableWidget()
        self.details_table.setColumnCount(4)
        self.details_table.setHorizontalHeaderLabels([
            "Produit", "Prix Unitaire", "Quantité", "Sous-total"
        ])
        
        # Configuration du tableau
        header = self.details_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        
        self.details_table.setAlternatingRowColors(True)
    
    def create_totals_section(self):
        """Créer la section des totaux"""
        layout = QGridLayout()
        
        layout.addWidget(QLabel("Sous-total:"), 0, 0)
        layout.addWidget(QLabel(f"{self.sale.total_amount:.0f} FBU"), 0, 1)
        
        layout.addWidget(QLabel("Remise:"), 1, 0)
        layout.addWidget(QLabel(f"{self.sale.discount:.0f} FBU"), 1, 1)
        
        total_label = QLabel("TOTAL:")
        total_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(total_label, 2, 0)
        
        final_amount_label = QLabel(f"{self.sale.final_amount:.0f} FBU")
        final_amount_label.setFont(QFont("Arial", 12, QFont.Bold))
        final_amount_label.setStyleSheet("color: #dc3545;")
        layout.addWidget(final_amount_label, 2, 1)
        
        return layout
    
    def load_sale_details(self):
        """Charger les détails de la vente"""
        self.details_table.setRowCount(len(self.sale.items))
        
        for row, item in enumerate(self.sale.items):
            # Produit
            product_name = item.product.name if item.product else "Produit supprimé"
            self.details_table.setItem(row, 0, QTableWidgetItem(product_name))
            
            # Prix unitaire
            self.details_table.setItem(row, 1, QTableWidgetItem(f"{item.unit_price:.0f} FBU"))
            
            # Quantité
            self.details_table.setItem(row, 2, QTableWidgetItem(str(item.quantity)))
            
            # Sous-total
            self.details_table.setItem(row, 3, QTableWidgetItem(f"{item.subtotal:.0f} FBU"))
