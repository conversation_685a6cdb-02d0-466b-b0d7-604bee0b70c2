# -*- coding: utf-8 -*-
"""
Fenêtre de gestion du stock
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QTableWidget, QTableWidgetItem, 
                             QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                             QMessageBox, QHeaderView, QFrame, QFormLayout,
                             QGroupBox, QGridLayout, QDateEdit, QTextEdit)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont
from models.database import SessionLocal
from models.product import Product
from models.supply import Supply
from controllers.auth_controller import auth_controller
from datetime import datetime

class StockManagement(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db = SessionLocal()
        self.init_ui()
        self.load_stock()
        
    def init_ui(self):
        """Initialiser l'interface utilisateur"""
        self.setWindowTitle("Gestion du Stock")
        self.setMinimumSize(1200, 800)
        self.setModal(True)
        
        # Layout principal
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # En-tête
        header = QLabel("GESTION DU STOCK")
        header.setFont(QFont("Arial", 18, QFont.Bold))
        header.setAlignment(Qt.AlignCenter)
        header.setStyleSheet("color: #2c3e50; margin-bottom: 20px;")
        main_layout.addWidget(header)
        
        # Section de recherche et filtres
        search_layout = self.create_search_section()
        main_layout.addLayout(search_layout)
        
        # Tableau du stock
        self.create_stock_table()
        main_layout.addWidget(self.stock_table)
        
        # Boutons d'action
        buttons_layout = self.create_buttons_section()
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
        # Style général
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                font-family: Arial, sans-serif;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QTableWidget {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                gridline-color: #dee2e6;
            }
            QTableWidget::item {
                padding: 8px;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
            QLineEdit, QComboBox {
                padding: 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: white;
            }
        """)
    
    def create_search_section(self):
        """Créer la section de recherche et filtres"""
        search_layout = QHBoxLayout()
        
        # Recherche par nom
        search_label = QLabel("Rechercher:")
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Nom du produit...")
        self.search_input.textChanged.connect(self.filter_stock)
        
        # Filtre par catégorie
        category_label = QLabel("Catégorie:")
        self.category_filter = QComboBox()
        self.category_filter.addItem("Toutes les catégories")
        self.load_categories()
        self.category_filter.currentTextChanged.connect(self.filter_stock)
        
        # Filtre par statut du stock
        status_label = QLabel("Statut Stock:")
        self.status_filter = QComboBox()
        self.status_filter.addItems(["Tous", "Stock normal", "Stock bas", "Rupture de stock"])
        self.status_filter.currentTextChanged.connect(self.filter_stock)
        
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        search_layout.addWidget(category_label)
        search_layout.addWidget(self.category_filter)
        search_layout.addWidget(status_label)
        search_layout.addWidget(self.status_filter)
        search_layout.addStretch()
        
        return search_layout
    
    def create_stock_table(self):
        """Créer le tableau du stock"""
        self.stock_table = QTableWidget()
        self.stock_table.setColumnCount(9)
        self.stock_table.setHorizontalHeaderLabels([
            "Produit", "Catégorie", "Stock Actuel", "Stock Min", 
            "Unité", "Prix Achat", "Prix Vente", "Valeur Stock", "Statut"
        ])
        
        # Configuration du tableau
        header = self.stock_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # Produit
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Catégorie
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Stock Actuel
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Stock Min
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Unité
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Prix Achat
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Prix Vente
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # Valeur Stock
        header.setSectionResizeMode(8, QHeaderView.ResizeToContents)  # Statut
        
        self.stock_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.stock_table.setAlternatingRowColors(True)
        self.stock_table.doubleClicked.connect(self.adjust_stock)
    
    def create_buttons_section(self):
        """Créer la section des boutons"""
        buttons_layout = QHBoxLayout()
        
        # Bouton Ajuster Stock
        adjust_button = QPushButton("Ajuster Stock")
        adjust_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                font-size: 14px;
                padding: 12px 24px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        adjust_button.clicked.connect(self.adjust_stock)
        
        # Bouton Approvisionnement
        supply_button = QPushButton("Approvisionnement")
        supply_button.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                font-size: 14px;
                padding: 12px 24px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        supply_button.clicked.connect(self.new_supply)
        
        # Bouton Historique
        history_button = QPushButton("Historique")
        history_button.setStyleSheet("""
            QPushButton {
                background-color: #ffc107;
                color: #212529;
                font-size: 14px;
                padding: 12px 24px;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
        """)
        history_button.clicked.connect(self.view_history)
        
        # Bouton Actualiser
        refresh_button = QPushButton("Actualiser")
        refresh_button.clicked.connect(self.load_stock)
        
        # Bouton Fermer
        close_button = QPushButton("Fermer")
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                font-size: 14px;
                padding: 12px 24px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        close_button.clicked.connect(self.close)
        
        buttons_layout.addWidget(adjust_button)
        buttons_layout.addWidget(supply_button)
        buttons_layout.addWidget(history_button)
        buttons_layout.addStretch()
        buttons_layout.addWidget(refresh_button)
        buttons_layout.addWidget(close_button)
        
        return buttons_layout
    
    def load_categories(self):
        """Charger les catégories disponibles"""
        try:
            categories = self.db.query(Product.category).distinct().all()
            for category in categories:
                if category[0]:  # Vérifier que la catégorie n'est pas None
                    self.category_filter.addItem(category[0])
        except Exception as e:
            print(f"Erreur lors du chargement des catégories: {e}")
    
    def load_stock(self):
        """Charger les données de stock dans le tableau"""
        try:
            products = self.db.query(Product).all()
            self.stock_table.setRowCount(len(products))
            
            for row, product in enumerate(products):
                # Produit
                self.stock_table.setItem(row, 0, QTableWidgetItem(product.name))
                
                # Catégorie
                self.stock_table.setItem(row, 1, QTableWidgetItem(product.category or ""))
                
                # Stock actuel
                stock_item = QTableWidgetItem(str(product.current_stock))
                if product.current_stock == 0:
                    stock_item.setBackground(Qt.red)
                    stock_item.setForeground(Qt.white)
                elif product.is_low_stock:
                    stock_item.setBackground(Qt.yellow)
                self.stock_table.setItem(row, 2, stock_item)
                
                # Stock minimum
                self.stock_table.setItem(row, 3, QTableWidgetItem(str(product.min_stock)))
                
                # Unité
                self.stock_table.setItem(row, 4, QTableWidgetItem(product.unit or ""))
                
                # Prix d'achat
                self.stock_table.setItem(row, 5, QTableWidgetItem(f"{product.purchase_price:.0f} FBU"))
                
                # Prix de vente
                self.stock_table.setItem(row, 6, QTableWidgetItem(f"{product.selling_price:.0f} FBU"))
                
                # Valeur du stock
                stock_value = product.current_stock * product.purchase_price
                self.stock_table.setItem(row, 7, QTableWidgetItem(f"{stock_value:.0f} FBU"))
                
                # Statut
                if product.current_stock == 0:
                    status = "Rupture de stock"
                elif product.is_low_stock:
                    status = "Stock bas"
                else:
                    status = "Stock normal"
                
                status_item = QTableWidgetItem(status)
                if status == "Rupture de stock":
                    status_item.setBackground(Qt.red)
                    status_item.setForeground(Qt.white)
                elif status == "Stock bas":
                    status_item.setBackground(Qt.yellow)
                self.stock_table.setItem(row, 8, status_item)
                
                # Stocker l'ID du produit dans la première colonne
                self.stock_table.item(row, 0).setData(Qt.UserRole, product.id)
                
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement du stock: {e}")
    
    def filter_stock(self):
        """Filtrer le stock selon les critères"""
        search_text = self.search_input.text().lower()
        category_filter = self.category_filter.currentText()
        status_filter = self.status_filter.currentText()
        
        for row in range(self.stock_table.rowCount()):
            show_row = True
            
            # Filtre par nom
            if search_text:
                name = self.stock_table.item(row, 0).text().lower()
                if search_text not in name:
                    show_row = False
            
            # Filtre par catégorie
            if category_filter != "Toutes les catégories":
                category = self.stock_table.item(row, 1).text()
                if category != category_filter:
                    show_row = False
            
            # Filtre par statut
            if status_filter != "Tous":
                status = self.stock_table.item(row, 8).text()
                if status != status_filter:
                    show_row = False
            
            self.stock_table.setRowHidden(row, not show_row)
    
    def adjust_stock(self):
        """Ajuster le stock du produit sélectionné"""
        current_row = self.stock_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "Attention", "Veuillez sélectionner un produit.")
            return
        
        product_id = self.stock_table.item(current_row, 0).data(Qt.UserRole)
        product = self.db.query(Product).filter(Product.id == product_id).first()
        
        if product:
            dialog = StockAdjustmentDialog(self, product)
            if dialog.exec_() == QDialog.Accepted:
                self.load_stock()
    
    def new_supply(self):
        """Créer un nouvel approvisionnement"""
        dialog = SupplyDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_stock()
    
    def view_history(self):
        """Voir l'historique des mouvements de stock"""
        dialog = StockHistoryDialog(self)
        dialog.exec_()
    
    def closeEvent(self, event):
        """Fermer la session de base de données"""
        self.db.close()
        event.accept()


class StockAdjustmentDialog(QDialog):
    def __init__(self, parent=None, product=None):
        super().__init__(parent)
        self.db = SessionLocal()
        self.product = product
        self.init_ui()

    def init_ui(self):
        """Initialiser l'interface du dialogue"""
        self.setWindowTitle(f"Ajustement de Stock - {self.product.name}")
        self.setMinimumSize(400, 300)
        self.setModal(True)

        # Layout principal
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # En-tête
        header = QLabel("AJUSTEMENT DE STOCK")
        header.setFont(QFont("Arial", 14, QFont.Bold))
        header.setAlignment(Qt.AlignCenter)
        header.setStyleSheet("color: #2c3e50; margin-bottom: 20px;")
        main_layout.addWidget(header)

        # Informations du produit
        info_layout = QFormLayout()
        info_layout.addRow("Produit:", QLabel(self.product.name))
        info_layout.addRow("Stock actuel:", QLabel(f"{self.product.current_stock} {self.product.unit or ''}"))
        info_layout.addRow("Stock minimum:", QLabel(f"{self.product.min_stock} {self.product.unit or ''}"))
        main_layout.addLayout(info_layout)

        # Type d'ajustement
        adjustment_layout = QFormLayout()

        self.adjustment_type = QComboBox()
        self.adjustment_type.addItems(["Ajouter au stock", "Retirer du stock", "Définir le stock"])
        adjustment_layout.addRow("Type d'ajustement:", self.adjustment_type)

        self.quantity_input = QSpinBox()
        self.quantity_input.setRange(0, 999999)
        adjustment_layout.addRow("Quantité:", self.quantity_input)

        self.reason_input = QTextEdit()
        self.reason_input.setMaximumHeight(80)
        self.reason_input.setPlaceholderText("Raison de l'ajustement...")
        adjustment_layout.addRow("Raison:", self.reason_input)

        main_layout.addLayout(adjustment_layout)

        # Boutons
        buttons_layout = QHBoxLayout()

        save_button = QPushButton("Enregistrer")
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        save_button.clicked.connect(self.save_adjustment)

        cancel_button = QPushButton("Annuler")
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        cancel_button.clicked.connect(self.reject)

        buttons_layout.addStretch()
        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(cancel_button)
        main_layout.addLayout(buttons_layout)

        self.setLayout(main_layout)

        # Style
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                font-family: Arial, sans-serif;
            }
            QLineEdit, QComboBox, QSpinBox, QTextEdit {
                padding: 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: white;
            }
        """)

    def save_adjustment(self):
        """Enregistrer l'ajustement de stock"""
        adjustment_type = self.adjustment_type.currentText()
        quantity = self.quantity_input.value()
        reason = self.reason_input.toPlainText().strip()

        if quantity <= 0:
            QMessageBox.warning(self, "Attention", "La quantité doit être supérieure à 0.")
            return

        if not reason:
            QMessageBox.warning(self, "Attention", "Veuillez indiquer la raison de l'ajustement.")
            return

        try:
            old_stock = self.product.current_stock

            if adjustment_type == "Ajouter au stock":
                self.product.update_stock(quantity, 'add')
            elif adjustment_type == "Retirer du stock":
                if quantity > self.product.current_stock:
                    QMessageBox.warning(self, "Attention", "Quantité insuffisante en stock.")
                    return
                self.product.update_stock(quantity, 'subtract')
            elif adjustment_type == "Définir le stock":
                self.product.current_stock = quantity

            self.product.updated_at = datetime.now()

            # Créer un enregistrement d'approvisionnement pour traçabilité
            current_user = auth_controller.get_current_user()
            supply = Supply(
                product_id=self.product.id,
                supplier="Ajustement de stock",
                quantity=quantity if adjustment_type == "Ajouter au stock" else -quantity,
                unit_cost=0,
                total_cost=0,
                supply_date=datetime.now(),
                notes=f"{adjustment_type}: {reason}",
                user_id=current_user.id
            )

            self.db.add(supply)
            self.db.commit()

            QMessageBox.information(
                self,
                "Succès",
                f"Stock ajusté avec succès!\nAncien stock: {old_stock}\nNouveau stock: {self.product.current_stock}"
            )
            self.accept()

        except Exception as e:
            self.db.rollback()
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ajustement: {e}")

    def closeEvent(self, event):
        """Fermer la session de base de données"""
        self.db.close()
        event.accept()


class SupplyDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db = SessionLocal()
        self.init_ui()
        self.load_products()

    def init_ui(self):
        """Initialiser l'interface du dialogue"""
        self.setWindowTitle("Nouvel Approvisionnement")
        self.setMinimumSize(500, 400)
        self.setModal(True)

        # Layout principal
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # En-tête
        header = QLabel("NOUVEL APPROVISIONNEMENT")
        header.setFont(QFont("Arial", 14, QFont.Bold))
        header.setAlignment(Qt.AlignCenter)
        header.setStyleSheet("color: #2c3e50; margin-bottom: 20px;")
        main_layout.addWidget(header)

        # Formulaire
        form_layout = QFormLayout()

        # Produit
        self.product_combo = QComboBox()
        form_layout.addRow("Produit *:", self.product_combo)

        # Fournisseur
        self.supplier_input = QLineEdit()
        self.supplier_input.setPlaceholderText("Nom du fournisseur")
        form_layout.addRow("Fournisseur *:", self.supplier_input)

        # Quantité
        self.quantity_input = QSpinBox()
        self.quantity_input.setRange(1, 999999)
        self.quantity_input.valueChanged.connect(self.calculate_total)
        form_layout.addRow("Quantité *:", self.quantity_input)

        # Coût unitaire
        self.unit_cost_input = QDoubleSpinBox()
        self.unit_cost_input.setRange(0, 999999)
        self.unit_cost_input.setSuffix(" FBU")
        self.unit_cost_input.valueChanged.connect(self.calculate_total)
        form_layout.addRow("Coût unitaire *:", self.unit_cost_input)

        # Coût total (calculé automatiquement)
        self.total_cost_label = QLabel("0 FBU")
        self.total_cost_label.setStyleSheet("font-weight: bold; color: #28a745;")
        form_layout.addRow("Coût total:", self.total_cost_label)

        # Date d'approvisionnement
        self.supply_date = QDateEdit()
        self.supply_date.setDate(QDate.currentDate())
        self.supply_date.setCalendarPopup(True)
        form_layout.addRow("Date:", self.supply_date)

        # Notes
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(80)
        self.notes_input.setPlaceholderText("Notes optionnelles...")
        form_layout.addRow("Notes:", self.notes_input)

        main_layout.addLayout(form_layout)

        # Boutons
        buttons_layout = QHBoxLayout()

        save_button = QPushButton("Enregistrer")
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        save_button.clicked.connect(self.save_supply)

        cancel_button = QPushButton("Annuler")
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        cancel_button.clicked.connect(self.reject)

        buttons_layout.addStretch()
        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(cancel_button)
        main_layout.addLayout(buttons_layout)

        self.setLayout(main_layout)

        # Style
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                font-family: Arial, sans-serif;
            }
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit, QTextEdit {
                padding: 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: white;
            }
        """)

    def load_products(self):
        """Charger les produits disponibles"""
        try:
            products = self.db.query(Product).filter(Product.is_active == "Oui").all()

            self.product_combo.clear()
            self.product_combo.addItem("Sélectionner un produit", None)

            for product in products:
                display_text = f"{product.name} (Stock: {product.current_stock})"
                self.product_combo.addItem(display_text, product)

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des produits: {e}")

    def calculate_total(self):
        """Calculer le coût total"""
        quantity = self.quantity_input.value()
        unit_cost = self.unit_cost_input.value()
        total = quantity * unit_cost
        self.total_cost_label.setText(f"{total:.0f} FBU")

    def save_supply(self):
        """Enregistrer l'approvisionnement"""
        product = self.product_combo.currentData()
        supplier = self.supplier_input.text().strip()
        quantity = self.quantity_input.value()
        unit_cost = self.unit_cost_input.value()

        # Validation
        if not product:
            QMessageBox.warning(self, "Attention", "Veuillez sélectionner un produit.")
            return

        if not supplier:
            QMessageBox.warning(self, "Attention", "Veuillez indiquer le fournisseur.")
            return

        if quantity <= 0:
            QMessageBox.warning(self, "Attention", "La quantité doit être supérieure à 0.")
            return

        if unit_cost <= 0:
            QMessageBox.warning(self, "Attention", "Le coût unitaire doit être supérieur à 0.")
            return

        try:
            # Créer l'approvisionnement
            current_user = auth_controller.get_current_user()
            supply = Supply(
                product_id=product.id,
                supplier=supplier,
                quantity=quantity,
                unit_cost=unit_cost,
                total_cost=quantity * unit_cost,
                supply_date=self.supply_date.date().toPyDate(),
                notes=self.notes_input.toPlainText().strip(),
                user_id=current_user.id
            )

            # Mettre à jour le stock du produit
            product.update_stock(quantity, 'add')
            product.updated_at = datetime.now()

            self.db.add(supply)
            self.db.commit()

            QMessageBox.information(
                self,
                "Succès",
                f"Approvisionnement enregistré avec succès!\nNouveau stock: {product.current_stock}"
            )
            self.accept()

        except Exception as e:
            self.db.rollback()
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'enregistrement: {e}")

    def closeEvent(self, event):
        """Fermer la session de base de données"""
        self.db.close()
        event.accept()


class StockHistoryDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db = SessionLocal()
        self.init_ui()
        self.load_history()

    def init_ui(self):
        """Initialiser l'interface du dialogue"""
        self.setWindowTitle("Historique des Mouvements de Stock")
        self.setMinimumSize(1000, 600)
        self.setModal(True)

        # Layout principal
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # En-tête
        header = QLabel("HISTORIQUE DES MOUVEMENTS DE STOCK")
        header.setFont(QFont("Arial", 16, QFont.Bold))
        header.setAlignment(Qt.AlignCenter)
        header.setStyleSheet("color: #2c3e50; margin-bottom: 20px;")
        main_layout.addWidget(header)

        # Filtres
        filters_layout = QHBoxLayout()

        # Filtre par produit
        product_label = QLabel("Produit:")
        self.product_filter = QComboBox()
        self.product_filter.addItem("Tous les produits")
        self.load_products_filter()
        self.product_filter.currentTextChanged.connect(self.filter_history)

        # Filtre par date
        date_label = QLabel("Date:")
        self.date_filter = QDateEdit()
        self.date_filter.setDate(QDate.currentDate())
        self.date_filter.setCalendarPopup(True)
        self.date_filter.dateChanged.connect(self.filter_history)

        filters_layout.addWidget(product_label)
        filters_layout.addWidget(self.product_filter)
        filters_layout.addWidget(date_label)
        filters_layout.addWidget(self.date_filter)
        filters_layout.addStretch()

        main_layout.addLayout(filters_layout)

        # Tableau de l'historique
        self.create_history_table()
        main_layout.addWidget(self.history_table)

        # Bouton fermer
        close_button = QPushButton("Fermer")
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        close_button.clicked.connect(self.close)

        close_layout = QHBoxLayout()
        close_layout.addStretch()
        close_layout.addWidget(close_button)
        main_layout.addLayout(close_layout)

        self.setLayout(main_layout)

        # Style
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                font-family: Arial, sans-serif;
            }
            QTableWidget {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                gridline-color: #dee2e6;
            }
            QTableWidget::item {
                padding: 8px;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
            QLineEdit, QComboBox, QDateEdit {
                padding: 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: white;
            }
        """)

    def create_history_table(self):
        """Créer le tableau de l'historique"""
        self.history_table = QTableWidget()
        self.history_table.setColumnCount(7)
        self.history_table.setHorizontalHeaderLabels([
            "Date", "Produit", "Type", "Quantité", "Fournisseur", "Coût Total", "Notes"
        ])

        # Configuration du tableau
        header = self.history_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Date
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # Produit
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Type
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Quantité
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Fournisseur
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Coût Total
        header.setSectionResizeMode(6, QHeaderView.Stretch)  # Notes

        self.history_table.setAlternatingRowColors(True)

    def load_products_filter(self):
        """Charger les produits pour le filtre"""
        try:
            products = self.db.query(Product).all()
            for product in products:
                self.product_filter.addItem(product.name, product.id)
        except Exception as e:
            print(f"Erreur lors du chargement des produits: {e}")

    def load_history(self):
        """Charger l'historique des mouvements"""
        try:
            supplies = self.db.query(Supply).order_by(Supply.supply_date.desc()).all()
            self.history_table.setRowCount(len(supplies))

            for row, supply in enumerate(supplies):
                # Date
                supply_date = supply.supply_date.strftime("%d/%m/%Y")
                self.history_table.setItem(row, 0, QTableWidgetItem(supply_date))

                # Produit
                product_name = supply.product.name if supply.product else "Produit supprimé"
                self.history_table.setItem(row, 1, QTableWidgetItem(product_name))

                # Type
                movement_type = "Entrée" if supply.quantity > 0 else "Sortie"
                type_item = QTableWidgetItem(movement_type)
                if movement_type == "Entrée":
                    type_item.setBackground(Qt.green)
                    type_item.setForeground(Qt.white)
                else:
                    type_item.setBackground(Qt.red)
                    type_item.setForeground(Qt.white)
                self.history_table.setItem(row, 2, type_item)

                # Quantité
                quantity_text = f"{abs(supply.quantity)} {supply.product.unit if supply.product else ''}"
                self.history_table.setItem(row, 3, QTableWidgetItem(quantity_text))

                # Fournisseur
                self.history_table.setItem(row, 4, QTableWidgetItem(supply.supplier or ""))

                # Coût total
                self.history_table.setItem(row, 5, QTableWidgetItem(f"{supply.total_cost:.0f} FBU"))

                # Notes
                self.history_table.setItem(row, 6, QTableWidgetItem(supply.notes or ""))

                # Stocker l'ID du produit pour le filtrage
                self.history_table.item(row, 1).setData(Qt.UserRole, supply.product_id if supply.product else None)

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement de l'historique: {e}")

    def filter_history(self):
        """Filtrer l'historique selon les critères"""
        product_filter = self.product_filter.currentData()
        selected_date = self.date_filter.date().toPyDate()

        for row in range(self.history_table.rowCount()):
            show_row = True

            # Filtre par produit
            if product_filter:
                row_product_id = self.history_table.item(row, 1).data(Qt.UserRole)
                if row_product_id != product_filter:
                    show_row = False

            # Filtre par date
            date_item = self.history_table.item(row, 0)
            if date_item:
                row_date = datetime.strptime(date_item.text(), "%d/%m/%Y").date()
                if row_date != selected_date:
                    show_row = False

            self.history_table.setRowHidden(row, not show_row)

    def closeEvent(self, event):
        """Fermer la session de base de données"""
        self.db.close()
        event.accept()
