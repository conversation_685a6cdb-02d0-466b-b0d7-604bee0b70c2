# -*- coding: utf-8 -*-
"""
Modèle Product pour la gestion des produits
"""

from sqlalchemy import Column, Integer, String, Float, DateTime, Text
from sqlalchemy.sql import func
from models.database import Base

class Product(Base):
    __tablename__ = 'products'

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    category = Column(String(50), nullable=False)  # Boissons, Plats, Snacks
    description = Column(Text, nullable=True)
    purchase_price = Column(Float, nullable=False, default=0.0)  # Prix d'achat
    selling_price = Column(Float, nullable=False, default=0.0)   # Prix de vente
    current_stock = Column(Integer, nullable=False, default=0)   # Stock actuel
    min_stock = Column(Integer, nullable=False, default=10)      # Stock minimum (alerte)
    unit = Column(String(20), nullable=False, default='pièce')   # Unité (pièce, litre, kg, etc.)
    barcode = Column(String(50), nullable=True, unique=True)     # Code-barres (optionnel)
    is_active = Column(String(10), nullable=False, default='Oui') # Produit actif
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self):
        return f"<Product(name='{self.name}', category='{self.category}', stock={self.current_stock})>"

    @property
    def is_low_stock(self):
        """Vérifier si le stock est bas"""
        return self.current_stock <= self.min_stock

    @property
    def profit_margin(self):
        """Calculer la marge bénéficiaire"""
        if self.purchase_price > 0:
            return ((self.selling_price - self.purchase_price) / self.purchase_price) * 100
        return 0

    def update_stock(self, quantity, operation='add'):
        """Mettre à jour le stock"""
        if operation == 'add':
            self.current_stock += quantity
        elif operation == 'subtract':
            self.current_stock = max(0, self.current_stock - quantity)
        elif operation == 'set':
            self.current_stock = max(0, quantity)

    def to_dict(self):
        """Convertir l'objet en dictionnaire"""
        return {
            'id': self.id,
            'name': self.name,
            'category': self.category,
            'description': self.description,
            'purchase_price': self.purchase_price,
            'selling_price': self.selling_price,
            'current_stock': self.current_stock,
            'min_stock': self.min_stock,
            'unit': self.unit,
            'barcode': self.barcode,
            'is_active': self.is_active,
            'is_low_stock': self.is_low_stock,
            'profit_margin': round(self.profit_margin, 2),
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
